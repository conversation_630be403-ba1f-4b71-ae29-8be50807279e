# 🤖 AI 编辑器学习项目

## 📋 项目概述

这是一个从零开始构建的 AI 编辑器学习项目，基于 Novel + TipTap + ProseMirror 技术栈。

## ✨ 功能特性

### 🎯 核心功能
- ✅ **基础编辑器** - 完整的富文本编辑功能
- ✅ **AI 工具栏** - 选中文字时自动显示 AI 功能
- ✅ **AI 助手面板** - 提供快速提示词和自定义指令
- ✅ **Slash 命令** - 输入 "/" 快速插入不同类型内容
- ✅ **图片拖拽** - 支持拖拽和粘贴图片上传
- ✅ **Markdown 输出** - 实时转换为 Markdown 格式
- ✅ **响应式设计** - 适配各种屏幕尺寸

### 🤖 AI 功能
- **改进文字** - 让文字更清晰、更有说服力
- **扩展内容** - 添加更多细节和例子
- **总结要点** - 提取核心要点和关键信息
- **修正语法** - 检查并修正语法和表达
- **自定义指令** - 支持用户自定义 AI 提示词

### ⚡ 交互体验
- **智能工具栏** - 选中文字时自动显示
- **快捷键支持** - Ctrl+K 打开 AI 助手
- **实时反馈** - 加载状态和错误处理
- **流畅动画** - 平滑的界面过渡效果

## 🏗️ 技术架构

### 三层架构设计
```
🎨 Novel (上层)     - React 组件封装 + UI 交互
⚙️ TipTap (中间层)  - 扩展系统 + API 封装  
🔧 ProseMirror (底层) - 核心引擎 + DOM 操作
```

### 核心组件
- **AIEditor** - 主编辑器组件
- **AIToolbar** - 浮动工具栏组件
- **AIAssistant** - AI 助手面板组件
- **Extensions** - 自定义扩展配置

## 🚀 使用方法

### 基础编辑
1. 在编辑器中开始写作
2. 使用标准的富文本编辑功能
3. 查看右侧的 Markdown 输出

### AI 功能
1. **选中文字** - 选中任意文字，工具栏会自动出现
2. **点击 AI 按钮** - 点击工具栏中的 AI 按钮
3. **选择操作** - 选择快速提示词或输入自定义指令
4. **应用结果** - 选择插入或替换生成的内容

### 快捷键
- `Ctrl+K` - 打开 AI 助手
- `Ctrl+Shift+A` - 快速 AI 生成（开发中）
- `/` - 打开 Slash 命令菜单

### Slash 命令
1. **输入 "/"** - 在空行输入斜杠打开命令菜单
2. **选择内容类型** - 文本、标题、列表、引用、代码块等
3. **快速插入** - 点击或回车确认插入

### 图片功能
1. **拖拽上传** - 直接拖拽图片文件到编辑器
2. **粘贴上传** - 复制图片后在编辑器中粘贴
3. **调整大小** - 点击图片显示调整手柄

## 📁 项目结构

```
src/components/ai-editor/
├── index.tsx          # 主编辑器组件
├── extensions.ts      # TipTap 扩展配置
├── slash-command.tsx  # Slash 命令组件
├── image-upload.ts    # 图片上传处理
├── ai-toolbar.tsx     # AI 工具栏组件
├── ai-assistant.tsx   # AI 助手面板组件
└── ai-editor.css      # 编辑器样式文件

src/app/ai-editor-learn/
├── page.tsx           # 演示页面
└── README.md          # 项目说明
```

## 🔧 开发进度

### ✅ 已完成
- [x] 基础编辑器功能
- [x] AI 工具栏实现
- [x] AI 助手面板
- [x] 文本选择处理
- [x] Slash 命令系统
- [x] 图片拖拽上传
- [x] 图片粘贴上传
- [x] 图片大小调整
- [x] 任务列表支持
- [x] Markdown 输出
- [x] 响应式布局
- [x] 页面简化优化

### 🔄 开发中
- [ ] 真实 AI API 集成
- [ ] 更多 AI 提示词模板
- [x] Slash 命令功能
- [x] 图片拖拽上传
- [ ] 样式系统优化

### ⏳ 计划中
- [ ] 插件系统扩展
- [ ] 协作编辑功能
- [ ] 导出功能增强
- [ ] 性能优化

## 🎯 学习价值

这个项目展示了：

1. **现代编辑器开发** - 从零开始构建富文本编辑器
2. **三层架构实践** - Novel + TipTap + ProseMirror 的实际应用
3. **AI 功能集成** - 如何将 AI 能力无缝集成到编辑器中
4. **React 最佳实践** - Hooks、状态管理、组件设计
5. **用户体验设计** - 交互设计和界面优化

## 🐛 已知问题

1. **样式兼容性** - Tailwind CSS v4 兼容性问题，暂时使用内联样式
2. **字体加载** - Google Fonts 加载问题，不影响功能使用
3. **AI 模拟** - 当前使用模拟 AI 响应，需要集成真实 API

## 🔗 相关链接

- [Novel 编辑器](https://novel.sh/)
- [TipTap 文档](https://tiptap.dev/)
- [ProseMirror 指南](https://prosemirror.net/)
- [DeerFlow 项目](https://github.com/bytedance/deer-flow)

## 📝 更新日志

### v1.0.0 (2025-01-25)
- ✨ 初始版本发布
- ✅ 基础编辑器功能完成
- ✅ AI 功能集成完成
- ✅ 页面简化优化完成

---

**开发者**: AI Assistant  
**技术栈**: Next.js + React + TypeScript + Novel + TipTap + ProseMirror  
**最后更新**: 2025-01-25
